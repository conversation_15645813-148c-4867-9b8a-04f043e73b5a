# File: UI.py

from PyQt5.QtWidgets import (QWidget, QHBoxLayout, QLabel, QPushButton, QFrame, QSplitter, QGroupBox, QVBoxLayout, QTabWidget,QScrollArea,
                             QDialog, QComboBox, QTabBar, QGridLayout, QSpacerItem, QSizePolicy, QFileDialog,QMenu, QAction, QMessageBox)
from PyQt5.QtGui import QPixmap, QIcon,  QImage
from PyQt5.QtCore import Qt, QSize, QFileInfo, QThread, pyqtSlot, QMetaObject, Q_ARG, pyqtSignal, QPoint
from Auto_Focus.focus_plot_window import FocusPlotWindow
from Camera.Main_Cam import Main_Camera
from Camera.Settings import SettingWindow
from Camera.Prev_Cam import USBPreview
from Scale_Adjust import ScaleAdjust
from Grbl.Grbl import Grbl
from Camera.RoiLabel import ROILabel
from Constant import *
from content_widget import AspectRatioLabel 
from Auto_Focus.AutoFocus_Worker import AutoFocusWorker
import cv2

'''
class UI sebagai Orkestrator

class UI dirancang sebagai main Thread yang menampung UI utama (main window), 
berisi berbagai widget dan method nya, serta memiliki komunikasi dengan 
Thread lainnya memalui Signal-Slot Qt. 

Prinsip Desaign:
1. Jaga Antarmuka tetap responsif (Gunakan Thread untuk tugas berat)
2. Hanya menerika data dari Thread lain (Jangan meminta / menarik data)
3. User Action Translation (Hanya mengirim Aksi dari User, tidak dijalankan di UI)
'''

class UI(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Slide Scanner")
        self.camera_thread = None
        self.camera = None
        self.camera_resolutions = []
        self.scale_adjust = ScaleAdjust(self.camera)
        self.grbl = Grbl()
        self.roi_label = None
        self.last_key = None

        self.valid_keys = valid_keys 

        self.setting_window = None 
        self.combo = None 
        
        self.lbl_video = None
        self.lbl_Prev = None
        self.lbl_live_preview_popup = None
        self.autofocus_thread = None
        self.autofocus_worker = None

        self.connect_grbl_on_startup()
        self.setup_ui()

        if self.lbl_live_preview_popup: # Label pop-up (di Live Stream Tab)
            if isinstance(self.lbl_live_preview_popup, ROILabel):
                self.lbl_live_preview_popup.display_changed.connect(self.on_roi_display_changed)
                self.lbl_live_preview_popup.pixel_clicked.connect(self._on_pixel_clicked)
                print(f"UI: Koneksi sinyal ROILabel (popup_preview_label) berhasil. ID: {id(self.lbl_live_preview_popup)}")
            else:
                print("UI: lbl_live_preview_popup bukan ROILabel, sinyal pixel_clicked tidak akan terhubung.")
        
        if self.roi_label: # Label di Stitch Tab
            self.roi_label.display_changed.connect(self.on_roi_display_changed)
            self.roi_label.pixel_clicked.connect(self._on_pixel_clicked)
            print(f"UI: Koneksi sinyal ROILabel (Stitch_tab) berhasil. ID: {id(self.roi_label)}")
  
        # Asumsi USBPreview bisa menerima banyak label.
        # Jika tidak, Anda perlu memodifikasi USBPreview untuk memancarkan sinyal
        # dan menghubungkannya ke slot .setPixmap() dari setiap label.
        self.preview_driver = USBPreview(
            self.lbl_Prev, 
            self.roi_label, 
            self.lbl_live_preview_popup, 
            cam_index=0, 
        )
        self.setup_main_camera_thread()
        self.setup_custom_tab() 
        self.scale_adjust.apply("10X")
        self.plot_window = None

        self.move_to_mode = False
        
    
    def setup_main_camera_thread(self):
        self.camera_thread = QThread()
        self.camera = Main_Camera()

        self.camera.moveToThread(self.camera_thread)

        # --- Hubungkan Sinyal dari Worker (Camera) ke Slot di UI ---
        self.camera.camera_opened.connect(self.on_camera_opened)
        self.camera.camera_closed.connect(self.on_camera_closed)
        self.camera.frame_ready.connect(self.update_main_video_feed)
        self.camera.fps_updated.connect(self.lbl_frame.setText)
        self.camera.still_image_saved.connect(self.on_still_image_saved)
        self.camera.error_occurred.connect(self.show_error_message)

        # Hubungkan sinyal start/stop thread
        self.camera_thread.started.connect(self.camera.start)
        # Saat UI ditutup, panggil slot 'stop' di worker
        self.aboutToClose.connect(lambda: QMetaObject.invokeMethod(self.camera, "stop", Qt.QueuedConnection))

        # Mulai thread
        self.camera_thread.start()

    # ======================================================================
    # SLOT PENERIMA DARI WORKER KAMERA
    # ======================================================================
    
    @pyqtSlot(str, list)
    def on_camera_opened(self, camera_name, resolutions):
        """Dipanggil saat kamera berhasil dibuka oleh worker."""
        print(f"UI: Menerima sinyal 'camera_opened'. Nama: {camera_name}")
        self.Name.setText(camera_name)
        self.camera_resolutions = resolutions # Simpan daftar resolusi
        self.Snap.setEnabled(True)
        self.setting.setEnabled(True)
        self.scale_adjust = ScaleAdjust(self.camera)
        self.scale_adjust.apply("10X")

    @pyqtSlot()
    def on_camera_closed(self):
        """Dipanggil saat kamera ditutup atau terputus."""
        print("UI: Menerima sinyal 'camera_closed'. Menonaktifkan kontrol.")
        self.Name.setText("Kamera Terputus")
        self.Snap.setEnabled(False)
        self.setting.setEnabled(False)

    @pyqtSlot(QPixmap)
    def update_main_video_feed(self, pixmap):
        """Menerima QPixmap dari worker dan menampilkannya."""
        if self.lbl_video and not pixmap.isNull():
            self.lbl_video.setPixmap(pixmap.scaled(
                self.lbl_video.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation
            ))

    @pyqtSlot(str)
    def on_still_image_saved(self, filename):
        QMessageBox.information(self, "Sukses", f"Gambar berhasil disimpan sebagai:\n{filename}")
        
    @pyqtSlot(str)
    def show_error_message(self, message):
        QMessageBox.critical(self, "Error Kamera", message)

    # ======================================================================
    # PENANGANAN AKSI UI
    # ======================================================================
    
    def handle_snap_button_click(self):
        """
        Logika ini sekarang berada di UI. UI menampilkan menu, lalu mengirim
        perintah 'snap' dengan indeks resolusi yang dipilih ke worker.
        """
        if not self.camera or not self.camera_resolutions:
            self.show_error_message("Kamera tidak siap atau tidak ada resolusi yang tersedia.")
            return

        menu = QMenu(self)
        for i, res_str in enumerate(self.camera_resolutions):
            action = QAction(res_str, self)
            action.setData(i)
            menu.addAction(action)
        
        selected_action = menu.exec(self.mapToGlobal(self.Snap.pos()))

        if selected_action:
            resolution_index = selected_action.data()
            print(f"UI: Meminta snap dengan resolusi indeks {resolution_index}")
            # Panggil slot 'snap' di worker thread dengan aman
            QMetaObject.invokeMethod(self.camera, "snap", Qt.QueuedConnection,
                                     Q_ARG(int, resolution_index))

    def change_setting(self):
        if self.combo and self.scale_adjust:
            pilihan = self.combo.currentText()
            print(f"Zoom diubah menjadi: {pilihan}")
            try:
                # Jika `apply` aman dipanggil, bisa langsung. Jika tidak, gunakan invokeMethod
                self.scale_adjust.apply(pilihan)
            except ValueError as e:
                print(e)
                
    # ======================================================================
    # SIKLUS HIDUP APLIKASI (LIFECYCLE)
    # ======================================================================

    def closeEvent(self, event):
        print("UI: Aplikasi akan ditutup...")
        # Pancarkan sinyal untuk memberi tahu worker agar bersiap berhenti
        self.aboutToClose.emit()
        
        # Hentikan thread preview camera
        if hasattr(self, 'preview_driver') and self.preview_driver:
            self.preview_driver.stop()
            
        # Tunggu thread kamera utama selesai
        if self.camera_thread and self.camera_thread.isRunning():
            print("UI: Menunggu camera_thread berhenti...")
            if not self.camera_thread.wait(3000): # Tunggu max 3 detik
                print("UI: Peringatan, thread kamera tidak berhenti dengan bersih.")
                self.camera_thread.terminate() # Opsi terakhir

        print("UI: Penutupan selesai.")
        super().closeEvent(event)
    
    # Sinyal kustom untuk menangani penutupan
    aboutToClose = pyqtSignal()
    
    def setup_ui(self):
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(10, 10, 10, 10)
        splitter = QSplitter(Qt.Horizontal)
        self._setup_left_panel(splitter)
        self._setup_right_panel(splitter)
        splitter.setSizes([1, 5])
        main_layout.addWidget(splitter)
        self.setLayout(main_layout)
        self._connect_jog_button_signals()
        self._apply_final_ui_settings()

    def _setup_left_panel(self, splitter_arg):
        self.camera_container = QFrame()
        self.camera_container.setFrameShape(QFrame.Box)
        container_layout = QVBoxLayout(self.camera_container)
        main_controls_gbox = self._create_main_controls_groupbox()
        container_layout.addWidget(main_controls_gbox)
        splitter_arg.addWidget(self.camera_container)

    def _create_main_controls_groupbox(self):
        gbox = QGroupBox()
        vlyt = QVBoxLayout(gbox)
        self.Name = QPushButton("oke")
        self.open = QPushButton("Open File...")
        self.open.clicked.connect(self.open_file)
        self.Snap = QPushButton("Snap")
        self.Snap.clicked.connect(self.handle_snap_button_click)
        self.Snap.setEnabled(False)
        self.setting = QPushButton("Setting")
        self.setting.clicked.connect(self.open_setting_window)
        vlyt.addWidget(self.Name)
        vlyt.addWidget(self.open)
        vlyt.addWidget(self.Snap)
        vlyt.addWidget(self.setting)
        vlyt.addItem(QSpacerItem(50, 50))
        grbl_controls_layout = self._create_grbl_controls_layout()
        vlyt.addLayout(grbl_controls_layout)
        status_box = self._create_status_layouts()
        vlyt.addWidget(status_box)
        ROI_Box = self._create_ROI_layout()
        vlyt.addWidget(ROI_Box)
        vlyt.addStretch()
        return gbox

    def _create_grbl_controls_layout(self):
        grid = QGridLayout()
        self.btn_up = self.create_button(Icon_up)
        self.btn_down = self.create_button(Icon_down)
        self.btn_right = self.create_button(Icon_right)
        self.btn_left = self.create_button(Icon_left)
        self.btn_zup = self.create_button(Icon_zup)
        self.btn_zdown = self.create_button(Icon_zdown)
        grbl_buttons = [self.btn_up, self.btn_down, self.btn_left, self.btn_right, self.btn_zup, self.btn_zdown]
        for btn in grbl_buttons:
            btn.setFocusPolicy(Qt.NoFocus)

        grid.addWidget(self.btn_up, 0, 1)
        grid.addWidget(self.btn_left, 1, 0)
        grid.addWidget(self.btn_down, 2, 1)
        grid.addWidget(self.btn_right, 1, 2)
        grid.addWidget(self.btn_zup, 0, 4)
        grid.addWidget(self.btn_zdown, 2, 4)
        return grid
    
    def _create_status_layouts(self):
        status_Box = QGroupBox()
        status_layout = QVBoxLayout()
        self.grbl_pos_label = QLabel("X: 0.00  Y: 0.00  Z: 0.00  [Disconnected]")
        self.btn_autofocus = QPushButton("Auto Focus")
        self.btn_autofocus.clicked.connect(self.on_autofocus_clicked)
        self.btn_refine_focus = QPushButton("Refine Focus")
        self.btn_refine_focus.clicked.connect(self.on_refine_focus_clicked)

        status_layout.addWidget(self.grbl_pos_label)
        status_layout.addWidget(self.btn_autofocus)
        status_layout.addWidget(self.btn_refine_focus)
        status_Box.setLayout(status_layout)
        return status_Box
    
    def _create_ROI_layout(self):
        ROI_Box = QGroupBox()
        ROI_layout = QVBoxLayout()
        self.roi_nom = QLabel("Roi DiPilih : - ")
        self.roi_set = QPushButton("Set ROI")
        self.roi_set.clicked.connect(self.display_roi_info)
        self.roi_crop = QPushButton("Cropping ROI")
        self.roi_crop.clicked.connect(self.crop_roi)
        self.roi_reset = QPushButton("Reset ROI")
        self.roi_reset.clicked.connect(self.reset_roi)
        self.roi_move = QPushButton("Move To")
        self.roi_move.clicked.connect(self._toggle_roi_pixel_mode)

        ROI_layout.addWidget(self.roi_nom)
        ROI_layout.addWidget(self.roi_set)
        ROI_layout.addWidget(self.roi_crop)
        ROI_layout.addWidget(self.roi_reset)
        ROI_layout.addWidget(self.roi_move)
        ROI_Box.setLayout(ROI_layout)
        return ROI_Box

    def _setup_right_panel(self, splitter_arg):
        self.device_container = QFrame()
        self.device_container.setFrameShape(QFrame.Box)
        container_layout = QVBoxLayout(self.device_container)
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabsClosable(True)
        self.tab_widget.tabCloseRequested.connect(self.close_tab)
        live_stream_tab_widget = self._create_live_stream_tab()
        preview_tab_widget = self._create_preview_tab()
        stitch_tab_widget = self._create_stitch_tab()
        self.tab_widget.addTab(live_stream_tab_widget, "Live Stream")
        #self.tab_widget.addTab(preview_tab_widget, "Preview Camera")
        self.tab_widget.addTab(stitch_tab_widget, "Stitch")
        for i in range(2): 
            tab_bar = self.tab_widget.tabBar()
            tab_bar.setTabButton(i, QTabBar.RightSide, None)
        self.tab_widget.setTabText(0, "")
        container_layout.addWidget(self.tab_widget)
        splitter_arg.addWidget(self.device_container)

    def _create_live_stream_tab(self):
        """ Membuat tab untuk Live Stream dengan pop-up preview yang menjaga rasio aspek. """
        self.live_stream_tab = QWidget()
        live_stream_layout = QVBoxLayout(self.live_stream_tab)

        video_container = QWidget()
        video_container.setFixedSize(1050, 850)

        self.lbl_video = QLabel(video_container)
        self.lbl_video.setAlignment(Qt.AlignCenter)
        self.lbl_video.setGeometry(0, 0, video_container.width(), video_container.height())

        # ======================================================================
        # 2. GUNAKAN AspectRatioLabel DAN HAPUS setScaledContents
        # ======================================================================
        self.lbl_live_preview_popup = ROILabel(video_container) # Ganti QLabel -> AspectRatioLabel
        self.lbl_live_preview_popup.setFrameShape(QFrame.Box)
        self.lbl_live_preview_popup.setLineWidth(1)
        
        # 3. ATUR UKURAN DENGAN RASIO ASPEK YANG BENAR (misal 4:3)
        # Kebanyakan webcam memiliki rasio 4:3 atau 16:9. Kita gunakan 4:3.
        popup_width = 240 # Lebar pop-up
        popup_height = int(popup_width * 3 / 4) # Hitung tinggi agar rasio 4:3 -> 180
        self.lbl_live_preview_popup.setFixedSize(popup_width, popup_height)

        margin = 10
        popup_x = video_container.width() - popup_width - margin
        popup_y = margin
        self.lbl_live_preview_popup.move(popup_x, popup_y)
        self.lbl_live_preview_popup.raise_()

        hbox_video = QHBoxLayout()
        hbox_video.addStretch(1)
        hbox_video.addWidget(video_container)
        hbox_video.addStretch(1)
        
        self.lbl_frame = QLabel()
        self.lbl_frame.setAlignment(Qt.AlignCenter)
        
        live_stream_layout.addLayout(hbox_video)
        live_stream_layout.addWidget(self.lbl_frame)
        
        return self.live_stream_tab

    def _create_preview_tab(self):
        self.Preview_tab = QWidget()
        preview_tab_layout = QVBoxLayout(self.Preview_tab)
        
        # Gunakan AspectRatioLabel juga di sini agar konsisten
        self.lbl_Prev = AspectRatioLabel()
        self.lbl_Prev.setAlignment(Qt.AlignCenter)
        # Tidak perlu setFixedSize agar bisa responsif, atau set jika memang harus
        
        hbox_prev = QHBoxLayout()
        hbox_prev.addStretch(1)
        hbox_prev.addWidget(self.lbl_Prev)
        hbox_prev.addStretch(1)
        preview_tab_layout.addLayout(hbox_prev)
        
        return self.Preview_tab

    def _create_stitch_tab(self):
        self.Stitch_tab = QWidget()
        main_layout = QVBoxLayout(self.Stitch_tab)

        self.roi_label = ROILabel()

        # 3. Masukkan semua ke layout utama
        main_layout.addStretch(1) # Pegas atas
        main_layout.addWidget(self.roi_label, 8) # Viewer (porsi 8)
        main_layout.addStretch(1) # Pegas bawah

        return self.Stitch_tab

    def _connect_jog_button_signals(self):
        button_to_key_map = { self.btn_up: Qt.Key_Up, self.btn_down: Qt.Key_Down, self.btn_left: Qt.Key_Left, self.btn_right: Qt.Key_Right, self.btn_zup: Qt.Key_PageUp, self.btn_zdown: Qt.Key_PageDown, }
        for button_widget, qt_key_enum in button_to_key_map.items():
            if qt_key_enum in self.valid_keys:
                direction, value = self.valid_keys[qt_key_enum]
                button_widget.pressed.connect( lambda d=direction, v=value: self.start_jog(d, v) )
                button_widget.released.connect(self.stop_jog)
            else:
                print(f"PERINGATAN: Tombol {button_widget} terpetakan ke key {qt_key_enum} yang tidak ada di self.valid_keys.")

    def _apply_final_ui_settings(self):
        self.setFocusPolicy(Qt.StrongFocus)
        self.setFocus()

    def open_setting_window(self):
        if self.setting_window is None or not self.setting_window.isVisible():
            # Kirim objek kamera (yang ada di worker thread) ke SettingWindow
            self.setting_window = SettingWindow(self.camera, self.grbl, self, preview_camera=self.preview_driver)
        
            # PENTING: Beri tahu SettingWindow tentang resolusi yang sudah kita dapatkan
            if self.camera_resolutions:
                 self.setting_window.populate_resolutions(self.camera_resolutions)
        
            self.setting_window.show()
        else:
            self.setting_window.raise_()
            self.setting_window.activateWindow()
    
    def closeEvent(self, event):
        if hasattr(self, 'preview_driver') and self.preview_driver:
            self.preview_driver.stop()
        super().closeEvent(event)
    
    def setup_custom_tab(self):
        layout = QHBoxLayout()
        label = QLabel("Live Stream:")
        self.combo = QComboBox()
        self.combo.addItems(zoom_size)
        self.combo.currentIndexChanged.connect(self.change_setting)
        layout.addWidget(label)
        layout.addWidget(self.combo)
        layout.setContentsMargins(5, 2, 5, 2) 
        widget = QWidget()
        widget.setLayout(layout)
        self.tab_widget.tabBar().setTabButton(0, QTabBar.LeftSide, widget)
    
    def change_setting(self):
        if self.combo:
            pilihan = self.combo.currentText()
            print(f"Zoom diubah menjadi: {pilihan}")
            try:
                self.scale_adjust.apply(pilihan)
            except ValueError as e:
                print(e)
    
    def connect_grbl_on_startup(self):
        self.grbl.connect_grbl()
        grbl_config =self.grbl.load_settings_from_Json("GRBL_Settings.json")
        self.grbl.flush_settings_grbl(grbl_config)
        self.grbl.position_updated.connect(self.update_grbl_position_label)
        
    def create_button(self,icon_path):
        btn = QPushButton()
        btn.setIcon(QIcon(icon_path))
        btn.setIconSize(QSize(32, 32)) 
        return btn
    
    def keyPressEvent(self, event):
        key = event.key()
        if event.isAutoRepeat():
            return
        if key in self.valid_keys:
            self.last_key = key
            direction, value = self.valid_keys[key]
            self.start_jog(direction, value)

    def keyReleaseEvent(self, event):
        key = event.key()
        if event.isAutoRepeat():
            return
        if key == self.last_key:
            self.last_key = None
            self.stop_jog()

    def start_jog(self, direction, value):
        self.grbl.jog(direction, value)

    def stop_jog(self):
        self.grbl.stop_jog()
    
    def close_tab(self, index):
        if index >= 3: 
            widget = self.tab_widget.widget(index)
            widget.deleteLater()
            self.tab_widget.removeTab(index)
            print(f"Tab di indeks {index} ditutup.")
        else:
            print(f"Tab '{self.tab_widget.tabText(index)}' tidak bisa ditutup.")
    
    def open_file(self):
        file_path, _ = QFileDialog.getOpenFileName( self, "Pilih Gambar", "", "Gambar (*.png *.jpg *.jpeg *.bmp *.gif);;Semua File (*.*)" )
        if file_path:
            file_info = QFileInfo(file_path)
            tab_name = file_info.fileName()
            new_tab_content = QWidget()
            tab_layout = QVBoxLayout(new_tab_content)
            image_label = AspectRatioLabel() # Ganti ke AspectRatioLabel agar gambar di tab juga bagus
            pixmap = QPixmap(file_path)
            if pixmap.isNull():
                image_label.setText("Gagal memuat gambar.")
                image_label.setAlignment(Qt.AlignCenter)
            else:
                image_label.setPixmap(pixmap)
                image_label.setAlignment(Qt.AlignCenter)
            scroll_area = QScrollArea()
            scroll_area.setWidgetResizable(True)
            scroll_area.setWidget(image_label)
            tab_layout.addWidget(scroll_area)
            self.tab_widget.addTab(new_tab_content, tab_name)
            self.tab_widget.setCurrentIndex(self.tab_widget.count() - 1)
        else:
            print("Pembukaan file dibatalkan.")
    
    def on_autofocus_clicked(self):
        if self.autofocus_thread and self.autofocus_thread.isRunning():
            reply = QMessageBox.question(self, "Batal?", "Proses auto focus sedang berjalan. Batalkan?",
                                         QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.Yes:
                self.autofocus_worker.stop_autofocus()
            return
            
        reply = QMessageBox.question(self, 'Mulai Auto Focus?',
                                     'Ini akan memulai proses auto focus.\nPastikan area sudah siap.',
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.No: return
        
        self.btn_autofocus.setText("AF Running...")
        self.btn_autofocus.setEnabled(False)
        
        self.autofocus_thread = QThread(self)
        self.autofocus_worker = AutoFocusWorker(self.camera, self.grbl)
        
        # Pindahkan kedua worker ke thread yang sama
        self.autofocus_worker.moveToThread(self.autofocus_thread)
        self.grbl.moveToThread(self.autofocus_thread)
        
        # Hubungkan sinyal dari worker ke UI
        self.autofocus_worker.status_changed.connect(self.lbl_frame.setText)
        self.autofocus_worker.focus_finished.connect(self.on_autofocus_finished)
        
        # Hubungkan siklus hidup thread
        self.autofocus_thread.started.connect(self.autofocus_worker.run_autofocus)
        self.autofocus_thread.finished.connect(self.autofocus_thread.deleteLater)
        self.autofocus_worker.destroyed.connect(lambda: print("AF Worker dihancurkan."))
        self.autofocus_thread.finished.connect(lambda: setattr(self, 'autofocus_thread', None))
        self.autofocus_worker.curve_data_ready.connect(self.show_focus_plots)
        
        self.autofocus_thread.start()

    # Buat slot baru ini untuk menerima sinyal
    @pyqtSlot(float, float, float, str)
    def update_grbl_position_label(self, x, y, z, status):
        """Memperbarui teks pada label posisi GRBL."""
        pos_text = f"X: {x:.3f}  Y: {y:.3f}  Z: {z:.3f}  [{status}]"
        self.grbl_pos_label.setText(pos_text)
    
    def on_refine_focus_clicked(self):
        """Memulai proses auto focus penyempurnaan (Hill Climbing)."""
        if self.autofocus_thread and self.autofocus_thread.isRunning():
            QMessageBox.information(self, "Info", "Proses Auto Focus sedang berjalan.")
            return

        # Logikanya hampir sama persis dengan on_autofocus_clicked
        self.btn_autofocus.setEnabled(False)
        self.btn_refine_focus.setEnabled(False)
        self.lbl_frame.setText("Memulai AF Penyempurnaan...")
        
        self.autofocus_thread = QThread(self)
        self.autofocus_worker = AutoFocusWorker(self.camera, self.grbl)
        self.autofocus_worker.moveToThread(self.autofocus_thread)
        self.grbl.moveToThread(self.autofocus_thread)
        
        # Hubungkan sinyal dari worker ke UI (sama seperti sebelumnya)
        self.autofocus_worker.status_changed.connect(self.lbl_frame.setText)
        self.autofocus_worker.focus_finished.connect(self.on_autofocus_finished) # Bisa menggunakan slot finish yang sama
        
        # --- PERBEDAAN UTAMA ADA DI SINI ---
        # Hubungkan sinyal 'started' ke metode 'run_refinement_autofocus'
        self.autofocus_thread.started.connect(self.autofocus_worker.run_refinement_autofocus)
        
        # ... (sisa koneksi finished dan deleteLater tetap sama)
        self.autofocus_thread.finished.connect(self.autofocus_worker.deleteLater)
        self.autofocus_thread.finished.connect(self.autofocus_thread.deleteLater)
        self.autofocus_thread.finished.connect(lambda: setattr(self, 'autofocus_thread', None))
        
        self.autofocus_thread.start()
        
    def on_autofocus_finished(self, best_pos, best_score):
        """Slot ini sekarang menangani hasil dari kedua jenis AF."""
        self.lbl_frame.setText(f"AF Selesai. Fokus terbaik di Z={best_pos:.3f}")
        
        self.grbl.moveToThread(self.thread())
        
        # Aktifkan kembali kedua tombol
        self.btn_autofocus.setEnabled(True)
        self.btn_refine_focus.setEnabled(True)
        
        if self.autofocus_thread:
            self.autofocus_thread.quit()
    
    @pyqtSlot(dict, dict)
    def show_focus_plots(self, coarse_data, fine_data):
        if self.plot_window is None or not self.plot_window.isVisible():
            self.plot_window = FocusPlotWindow(self)
        
        # Kirim kedua set data ke jendela plot
        self.plot_window.update_plots(coarse_data, fine_data)
        
        self.plot_window.show()
        self.plot_window.activateWindow()

    def display_roi_info(self):
        """
        Mengambil data ROI dari ImageViewer dan menampilkannya
        dalam format yang mudah dibaca.
        """
        # Panggil metode yang sudah ada di ImageViewer untuk mendapatkan koordinat piksel asli
        roi_rect = self.roi_label._get_roi_in_pixmap_coords()
        
        if not roi_rect:
            QMessageBox.warning(self, "Info", "Tidak ada ROI yang valid untuk dianalisis.")
            return
            
        # --- INI LOGIKA UTAMANYA ---
        
        # Ambil titik sudut kiri atas
        top_left = roi_rect.topLeft()
        x1 = top_left.x()
        y1 = top_left.y()
        
        # Ambil titik sudut kanan bawah
        bottom_right = roi_rect.bottomRight()
        x2 = bottom_right.x()
        y2 = bottom_right.y()
        
        # Ambil ukuran (lebar dan tinggi)
        lebar = roi_rect.width()
        tinggi = roi_rect.height()
        
        # Tampilkan hasilnya di konsol dengan format yang jelas
        print("="*40)
        print("      ANALISIS UKURAN ROI (PIKSEL ASLI)      ")
        print("="*40)
        print(f"Resolusi Gambar Asli: 1920 x 1080")
        print("-" * 40)
        print(f"Titik Awal (Kiri Atas):   (x1, y1) = ({x1}, {y1})")
        print(f"Titik Akhir (Kanan Bawah): (x2, y2) = ({x2}, {y2})")
        print("-" * 40)
        print(f"Besaran ROI (Lebar x Tinggi): {lebar} x {tinggi} piksel")
        print("="*40)
        
        # Anda juga bisa menampilkannya di QMessageBox
        info_text = (
            f"Titik Awal (x1, y1): ({x1}, {y1})\n"
            f"Titik Akhir (x2, y2): ({x2}, {y2})\n\n"
            f"Ukuran ROI: {lebar} x {tinggi} piksel"
        )
        QMessageBox.information(self, "Informasi Ukuran ROI", info_text)
    
    @pyqtSlot(QPixmap)
    def on_roi_display_changed(self, new_pixmap):
        """
        Dipanggil saat gambar di ROILabel di-crop atau di-reset.
        """
        if not self.preview_driver:
            return
        
        # Jika pixmap kosong, berarti ini adalah reset
        if new_pixmap.isNull():
            print("UI: Menerima sinyal reset, kembali ke streaming normal.")
            self.preview_driver.clear_override()
        else:
            # Jika ada pixmap, berarti ini hasil crop
            print("UI: Menerima gambar crop, mengaktifkan override di Prev_Cam.")
            self.preview_driver.set_override_image(new_pixmap)
    
    @pyqtSlot()
    def _toggle_roi_pixel_mode(self):
        """Mengganti mode ROILabel antara ROI Selection dan Pixel Coordinates."""
        roi_mode = self.roi_label.mode
        pop_mode = self.lbl_live_preview_popup.mode
        if roi_mode == self.roi_label.MODE_ROI_SELECTION and pop_mode == self.lbl_live_preview_popup.MODE_ROI_SELECTION:
            self.roi_label.set_mode(self.roi_label.MODE_PIXEL_COORDS)
            self.lbl_live_preview_popup.set_mode(self.lbl_live_preview_popup.MODE_PIXEL_COORDS)
            print("Move To Aktif")
            QMessageBox.information(self, "Mode Aktif", "Mode Pengambilan Koordinat Piksel Aktif.\nKlik pada gambar untuk mendapatkan koordinat piksel.")
        else:
            self.roi_label.set_mode(self.roi_label.MODE_ROI_SELECTION)
            self.lbl_live_preview_popup.set_mode(self.lbl_live_preview_popup.MODE_ROI_SELECTION)
            print("Move To Tidak Aktif")
            QMessageBox.information(self, "Mode Aktif", "Mode Seleksi ROI Aktif.")

    @pyqtSlot(QPoint)
    def _on_pixel_clicked(self, pixel_coords):
        """Slot untuk menerima dan menampilkan koordinat piksel yang diklik."""
        print(f"Piksel diklik pada koordinat: X={pixel_coords.x()}, Y={pixel_coords.y()}")
        QMessageBox.information(self, "Koordinat Piksel", 
                                f"Anda mengklik pada koordinat piksel:\nX: {pixel_coords.x()}\nY: {pixel_coords.y()}")
    
    def crop_roi(self):
        self.roi_label.crop_to_roi()
        self.lbl_live_preview_popup.crop_to_roi()
    
    def reset_roi(self):
        self.roi_label.reset_view()
        self.lbl_live_preview_popup.reset_view()
    
    
    
