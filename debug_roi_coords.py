#!/usr/bin/env python3
"""
Debug script untuk menguji koordinat ROI relatif.
Jalankan script ini dan ikuti instruksi untuk menguji:
1. Pilih ROI
2. Aktifkan Move To
3. Klik pada area ROI
4. Lihat output debugging
"""

import sys
import os

try:
    sys.path.append(os.path.join(os.path.dirname(__file__), 'Software'))
    from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton, QTextEdit
    from PyQt5.QtCore import QRect, QPoint, QSize
    from PyQt5.QtGui import QPixmap
    from Camera.RoiLabel import ROILabel
    print("✓ Import berhasil")
except ImportError as e:
    print(f"✗ Error import: {e}")
    sys.exit(1)

class DebugROIWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Debug ROI Coordinates")
        self.setGeometry(100, 100, 1000, 700)
        
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # Info label
        info_label = QLabel("DEBUG ROI COORDINATES")
        info_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        layout.addWidget(info_label)
        
        # ROI Label untuk testing
        self.roi_label = ROILabel()
        self.roi_label.setMinimumSize(600, 400)
        self.roi_label.setStyleSheet("border: 2px solid blue;")
        layout.addWidget(self.roi_label)
        
        # Buttons
        btn_layout = QVBoxLayout()
        
        self.btn_set_roi = QPushButton("1. Set Test ROI (100,100 200x200)")
        self.btn_set_roi.clicked.connect(self.set_test_roi)
        btn_layout.addWidget(self.btn_set_roi)
        
        self.btn_toggle_mode = QPushButton("2. Toggle Mode (ROI Selection / Move To)")
        self.btn_toggle_mode.clicked.connect(self.toggle_mode)
        btn_layout.addWidget(self.btn_toggle_mode)
        
        self.btn_test_coords = QPushButton("3. Test Coordinates (150,150)")
        self.btn_test_coords.clicked.connect(self.test_coordinates)
        btn_layout.addWidget(self.btn_test_coords)
        
        self.btn_clear_roi = QPushButton("4. Clear ROI")
        self.btn_clear_roi.clicked.connect(self.clear_roi)
        btn_layout.addWidget(self.btn_clear_roi)
        
        layout.addLayout(btn_layout)
        
        # Debug output
        self.debug_output = QTextEdit()
        self.debug_output.setMaximumHeight(200)
        self.debug_output.setPlainText("Debug output akan muncul di sini...\n")
        layout.addWidget(self.debug_output)
        
        self.setLayout(layout)
        
        # Setup test pixmap
        self.setup_test_pixmap()
        
        # Connect signals
        self.roi_label.pixel_clicked.connect(self.on_pixel_clicked)
        
    def setup_test_pixmap(self):
        """Setup test pixmap untuk ROI label"""
        # Buat pixmap test (simulasi gambar 1920x1080)
        pixmap = QPixmap(1920, 1080)
        pixmap.fill()  # Fill dengan warna putih
        self.roi_label.setPixmap(pixmap)
        self.log("Test pixmap 1920x1080 telah diset")
        
    def log(self, message):
        """Tambahkan pesan ke debug output"""
        self.debug_output.append(f"[DEBUG] {message}")
        print(f"[DEBUG] {message}")
        
    def set_test_roi(self):
        """Set ROI test secara manual"""
        self.log("Setting test ROI...")
        
        # Simulasi ROI selection
        # ROI dari (100,100) dengan ukuran 200x200 dalam koordinat screen
        # Kita perlu menghitung posisi screen yang sesuai
        
        # Untuk simulasi, kita set langsung ROI bounds
        roi_bounds = QRect(100, 100, 200, 200)  # ROI dalam koordinat original pixmap
        self.roi_label.set_active_roi_bounds(roi_bounds)
        
        # Set juga roi_rect untuk visualisasi (dalam koordinat screen)
        # Ini adalah approximation - dalam implementasi nyata dihitung dari mouse events
        self.roi_label.roi_rect = QRect(50, 50, 100, 100)  # Contoh koordinat screen
        self.roi_label.update()
        
        self.log(f"ROI bounds set: {roi_bounds.x()},{roi_bounds.y()} {roi_bounds.width()}x{roi_bounds.height()}")
        
    def toggle_mode(self):
        """Toggle antara ROI selection dan pixel coordinates mode"""
        current_mode = self.roi_label.mode
        if current_mode == self.roi_label.MODE_ROI_SELECTION:
            self.roi_label.set_mode(self.roi_label.MODE_PIXEL_COORDS)
            self.log("Mode changed to: PIXEL_COORDS (Move To)")
        else:
            self.roi_label.set_mode(self.roi_label.MODE_ROI_SELECTION)
            self.log("Mode changed to: ROI_SELECTION")
            
    def test_coordinates(self):
        """Test koordinat pada posisi tertentu"""
        self.log("Testing coordinates at screen point (150, 150)...")
        
        # Simulasi klik pada posisi (150, 150) dalam koordinat screen
        test_point = QPoint(150, 150)
        
        # Test koordinat absolut
        abs_coords = self.roi_label._get_pixel_coords_from_screen_point_absolute(test_point)
        if abs_coords:
            self.log(f"Absolute coordinates: {abs_coords.x()}, {abs_coords.y()}")
        else:
            self.log("Failed to get absolute coordinates")
            
        # Test koordinat relatif
        rel_coords = self.roi_label._get_pixel_coords_relative_to_roi(test_point)
        if rel_coords:
            self.log(f"Relative coordinates: {rel_coords.x()}, {rel_coords.y()}")
        else:
            self.log("Failed to get relative coordinates")
            
        # Test method utama
        main_coords = self.roi_label._get_pixel_coords_from_screen_point(test_point)
        if main_coords:
            self.log(f"Main method coordinates: {main_coords.x()}, {main_coords.y()}")
        else:
            self.log("Failed to get coordinates from main method")
            
    def clear_roi(self):
        """Clear ROI"""
        self.log("Clearing ROI...")
        self.roi_label.clear_roi()
        self.log("ROI cleared")
        
    def on_pixel_clicked(self, pixel_coords):
        """Handle pixel click event"""
        self.log(f"Pixel clicked: {pixel_coords.x()}, {pixel_coords.y()}")
        
        # Cek ROI bounds
        roi_bounds = self.roi_label.get_active_roi_bounds()
        if not roi_bounds.isNull() and roi_bounds.isValid():
            self.log(f"ROI bounds active: {roi_bounds.x()},{roi_bounds.y()} {roi_bounds.width()}x{roi_bounds.height()}")
            self.log("Coordinates should be RELATIVE to ROI")
        else:
            self.log("No ROI bounds active - coordinates are ABSOLUTE")

def main():
    app = QApplication(sys.argv)
    
    widget = DebugROIWidget()
    widget.show()
    
    print("Debug ROI Coordinates")
    print("=" * 50)
    print("Instruksi:")
    print("1. Klik 'Set Test ROI' untuk set ROI test")
    print("2. Klik 'Toggle Mode' untuk switch ke Move To mode")
    print("3. Klik 'Test Coordinates' untuk test koordinat")
    print("4. Atau klik langsung pada gambar setelah mode Move To")
    print("5. Lihat debug output di bawah")
    print("=" * 50)
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
