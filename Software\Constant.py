# File: Constant.py

from PyQt5.QtCore import QSize, Qt
"""File berisi nilai Constant yang digunakan di beberapa file lain."""

valid_keys = {
            Qt.Key_Up: ("Y", 65), # Sumbu Y menuju nilai 65
            Qt.Key_Down: ("Y", 0), # Sumbu Y menuju nilai 0
            Qt.Key_Left: ("X", 0), # Sumbu X menuju nilai 0
            Qt.Key_Right: ("X", 75), # Sumbu X menuju nilai 75
            Qt.Key_PageUp: ("Z", 11), # Sumbu Z menuju nilai 11
            Qt.Key_PageDown: ("Z", 0) # Sumbu Z menuju nilai 0
        }

zoom_size = ["4X", "10X", "20X", "40X"] # Daftar ukuran zoom yang tersedia

# ---path Icon untuk UI---
Icon_up = "Icons/up.png"
Icon_down = "Icons/down.png"
Icon_left = "Icons/left.png"
Icon_right = "Icons/right.png"
Icon_zup = "Icons/Dup.png"
Icon_zdown = "Icons/Ddown.png"
Icon_next = 'Icons/next.png'

# ---path untuk ffc---
ffc_4 = "ffc_4X.ffc"
ffc_10 = "ffc_10X.ffc"
ffc_40 = "ffc_40X.ffc"
ffc_100 = "ffc_100X.ffc"
ffc_prev = "Preview.ffc"

ZOOM_SETTINGS = {
    '4X': {
        'autoExp' : 0,
        'expTime': 207,
        'expGain': 100,     
        'temp': 5430,
        'tint': 990,
        'ffc_file': ffc_4 
    },
    '10X': { 
        'autoExp' : 0,
        'expTime': 270,
        'expGain': 100,
        'temp': 5437,
        'tint': 986,
        'ffc_file': ffc_10
    },
    '20X': {
        'autoExp' : 0,
        'expTime': 2526,
        'expGain': 100,
        'temp': 5322,
        'tint': 1001,
        'ffc_file': ffc_40
    },
    '40X': {
        'autoExp' : 0,
        'expTime': 20208,
        'expGain': 100,
        'temp': 5265,
        'tint': 989,
        'ffc_file': ffc_100
    }
}

