#!/usr/bin/env python3
"""
Simple test untuk debugging ROI coordinates tanpa GUI kompleks
"""

import sys
import os

try:
    sys.path.append(os.path.join(os.path.dirname(__file__), 'Software'))
    from PyQt5.QtCore import QRect, QPoint, QSize
    from PyQt5.QtGui import QPixmap
    from Camera.RoiLabel import ROILabel
    print("✓ Import berhasil")
except ImportError as e:
    print(f"✗ Error import: {e}")
    sys.exit(1)

def test_roi_coordinate_conversion():
    """Test konversi koordinat ROI"""
    print("\n=== Test ROI Coordinate Conversion ===")
    
    # Buat ROILabel instance
    roi_label = ROILabel()
    
    # Setup pixmap (simulasi gambar 1920x1080)
    pixmap = QPixmap(1920, 1080)
    roi_label.original_pixmap = pixmap
    roi_label.display_pixmap = pixmap
    
    # Test case: ROI dari (100,100) dengan ukuran 200x200 dalam koordinat pixmap
    roi_bounds_pixmap = QRect(100, 100, 200, 200)
    
    print(f"Setting ROI bounds: {roi_bounds_pixmap.x()}, {roi_bounds_pixmap.y()}, {roi_bounds_pixmap.width()}x{roi_bounds_pixmap.height()}")
    roi_label.set_active_roi_bounds(roi_bounds_pixmap)
    
    # Verifikasi ROI bounds tersimpan
    stored_bounds = roi_label.get_active_roi_bounds()
    print(f"Stored ROI bounds: {stored_bounds.x()}, {stored_bounds.y()}, {stored_bounds.width()}x{stored_bounds.height()}")
    
    if stored_bounds == roi_bounds_pixmap:
        print("✓ ROI bounds correctly stored")
    else:
        print("✗ ROI bounds storage failed")
        return False
    
    return True

def test_relative_coordinate_calculation():
    """Test kalkulasi koordinat relatif"""
    print("\n=== Test Relative Coordinate Calculation ===")
    
    # Setup ROI bounds: area dari (100,100) dengan ukuran 200x200
    roi_bounds = QRect(100, 100, 200, 200)
    
    # Test cases: (absolute_x, absolute_y, expected_relative_x, expected_relative_y)
    test_cases = [
        (100, 100, 0, 0),      # Sudut kiri atas ROI
        (150, 150, 50, 50),    # Tengah ROI
        (299, 299, 199, 199),  # Sudut kanan bawah ROI
        (200, 200, 100, 100),  # Tengah ROI
        (50, 50, 0, 0),        # Di luar ROI (kiri atas) - should clamp to 0,0
        (350, 350, 199, 199),  # Di luar ROI (kanan bawah) - should clamp to max
    ]
    
    print("Testing coordinate conversion:")
    print("Format: (abs_x, abs_y) → (rel_x, rel_y) [expected]")
    
    all_passed = True
    for abs_x, abs_y, exp_rel_x, exp_rel_y in test_cases:
        # Manual calculation
        rel_x = abs_x - roi_bounds.x()
        rel_y = abs_y - roi_bounds.y()
        
        # Clamp to ROI bounds
        rel_x = max(0, min(rel_x, roi_bounds.width() - 1))
        rel_y = max(0, min(rel_y, roi_bounds.height() - 1))
        
        if rel_x == exp_rel_x and rel_y == exp_rel_y:
            print(f"  ✓ ({abs_x:3d},{abs_y:3d}) → ({rel_x:3d},{rel_y:3d}) [({exp_rel_x:3d},{exp_rel_y:3d})]")
        else:
            print(f"  ✗ ({abs_x:3d},{abs_y:3d}) → ({rel_x:3d},{rel_y:3d}) [({exp_rel_x:3d},{exp_rel_y:3d})] FAILED")
            all_passed = False
    
    return all_passed

def test_coordinate_ranges():
    """Test rentang koordinat yang valid"""
    print("\n=== Test Coordinate Ranges ===")
    
    # ROI: 100,100 dengan ukuran 200x200
    roi_bounds = QRect(100, 100, 200, 200)
    
    print(f"ROI Bounds: ({roi_bounds.x()}, {roi_bounds.y()}) to ({roi_bounds.x() + roi_bounds.width()}, {roi_bounds.y() + roi_bounds.height()})")
    print(f"ROI Size: {roi_bounds.width()} x {roi_bounds.height()} pixels")
    print(f"Valid relative coordinates: (0, 0) to ({roi_bounds.width()-1}, {roi_bounds.height()-1})")
    
    # Test edge cases
    print("\nEdge case testing:")
    edge_cases = [
        (roi_bounds.x(), roi_bounds.y(), "Top-left corner"),
        (roi_bounds.x() + roi_bounds.width() - 1, roi_bounds.y() + roi_bounds.height() - 1, "Bottom-right corner"),
        (roi_bounds.x() + roi_bounds.width() // 2, roi_bounds.y() + roi_bounds.height() // 2, "Center"),
    ]
    
    for abs_x, abs_y, description in edge_cases:
        rel_x = abs_x - roi_bounds.x()
        rel_y = abs_y - roi_bounds.y()
        print(f"  {description}: ({abs_x},{abs_y}) → ({rel_x},{rel_y})")
    
    return True

def main():
    print("Test ROI Relative Coordinates - Simple Debug")
    print("=" * 60)
    
    # Run tests
    test1_passed = test_roi_coordinate_conversion()
    test2_passed = test_relative_coordinate_calculation()
    test3_passed = test_coordinate_ranges()
    
    print("\n" + "=" * 60)
    print("Test Results:")
    print(f"  ROI Coordinate Conversion: {'✓ PASSED' if test1_passed else '✗ FAILED'}")
    print(f"  Relative Coordinate Calculation: {'✓ PASSED' if test2_passed else '✗ FAILED'}")
    print(f"  Coordinate Ranges: {'✓ PASSED' if test3_passed else '✗ FAILED'}")
    
    if all([test1_passed, test2_passed, test3_passed]):
        print("\n🎉 All tests PASSED!")
        print("\nImplementasi ROI relative coordinates sudah benar.")
        print("Masalah mungkin ada di:")
        print("1. ROI bounds tidak ter-set saat ROI selection di UI")
        print("2. Sinkronisasi antara screen coordinates dan pixmap coordinates")
        print("3. Event handling di UI")
    else:
        print("\n❌ Some tests FAILED!")
        print("Ada masalah dalam logika konversi koordinat.")
    
    print("\nUntuk debugging lebih lanjut:")
    print("1. Pastikan ROI selection menghasilkan ROI bounds yang valid")
    print("2. Cek output console saat pilih ROI dan klik Move To")
    print("3. Verifikasi bahwa active_roi_bounds ter-set dengan benar")

if __name__ == "__main__":
    main()
