#!/usr/bin/env python3
"""
Test script untuk memverifikasi fungsi ROI yang baru ditambahkan.
Script ini akan menguji apakah:
1. Label roi_nom dapat diupdate dengan informasi ROI
2. Informasi ROI tetap ada setelah crop
3. Reset ROI berfungsi dengan benar
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'Software'))

from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton
from PyQt5.QtCore import QRect, QPoint
from PyQt5.QtGui import QPixmap
from Camera.RoiLabel import ROILabel

class TestROIWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Test ROI Functionality")
        self.setGeometry(100, 100, 800, 600)
        
        # Simulasi variabel yang ada di UI.py
        self.last_roi_info = None
        
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # Label untuk menampilkan informasi ROI (seperti self.roi_nom di UI.py)
        self.roi_nom = QLabel("ROI Dipilih: -")
        layout.addWidget(self.roi_nom)
        
        # ROI Label untuk testing
        self.roi_label = ROILabel()
        self.roi_label.setMinimumSize(400, 300)
        layout.addWidget(self.roi_label)
        
        # Tombol untuk testing
        btn_test_roi = QPushButton("Test Update ROI Info")
        btn_test_roi.clicked.connect(self.test_update_roi_info)
        layout.addWidget(btn_test_roi)
        
        btn_test_crop = QPushButton("Test Crop ROI")
        btn_test_crop.clicked.connect(self.test_crop_roi)
        layout.addWidget(btn_test_crop)
        
        btn_test_reset = QPushButton("Test Reset ROI")
        btn_test_reset.clicked.connect(self.test_reset_roi)
        layout.addWidget(btn_test_reset)
        
        self.setLayout(layout)
        
        # Setup test pixmap
        self.setup_test_pixmap()
        
        # Connect ROI selection event
        if hasattr(self.roi_label, 'mouseReleaseEvent'):
            original_mouse_release = self.roi_label.mouseReleaseEvent
            def custom_mouse_release(event):
                original_mouse_release(event)
                self.update_roi_info()
            self.roi_label.mouseReleaseEvent = custom_mouse_release
    
    def setup_test_pixmap(self):
        """Setup test pixmap untuk ROI label"""
        # Buat pixmap test sederhana
        pixmap = QPixmap(640, 480)
        pixmap.fill()  # Fill dengan warna putih
        self.roi_label.setPixmap(pixmap)
        
    def update_roi_info(self):
        """
        Update informasi ROI pada label roi_nom berdasarkan ROI yang dipilih.
        Ini adalah copy dari method yang sama di UI.py
        """
        roi_size_info = None
        
        if self.roi_label and hasattr(self.roi_label, 'get_roi_size'):
            roi_size_info = self.roi_label.get_roi_size()
        
        if roi_size_info and roi_size_info['pixmap_size']:
            # Simpan informasi ROI untuk dipertahankan setelah crop
            self.last_roi_info = roi_size_info
            
            pixmap_size = roi_size_info['pixmap_size']
            lebar = pixmap_size['width']
            tinggi = pixmap_size['height']
            
            # Update label dengan informasi ROI
            roi_text = f"ROI Dipilih: {lebar} x {tinggi} px"
            self.roi_nom.setText(roi_text)
            
            print(f"Test: ROI info updated - {lebar} x {tinggi} piksel")
        else:
            # Jika tidak ada ROI yang valid, tampilkan informasi terakhir jika ada
            if self.last_roi_info and self.last_roi_info['pixmap_size']:
                pixmap_size = self.last_roi_info['pixmap_size']
                lebar = pixmap_size['width']
                tinggi = pixmap_size['height']
                roi_text = f"ROI Terakhir: {lebar} x {tinggi} px"
                self.roi_nom.setText(roi_text)
            else:
                self.roi_nom.setText("ROI Dipilih: -")
    
    def test_update_roi_info(self):
        """Test manual update ROI info"""
        print("Testing update_roi_info...")
        self.update_roi_info()
        
    def test_crop_roi(self):
        """Test crop ROI functionality"""
        print("Testing crop_roi...")
        # Simpan informasi ROI sebelum crop
        self.update_roi_info()
        
        # Lakukan crop
        if hasattr(self.roi_label, 'crop_to_roi'):
            self.roi_label.crop_to_roi()
        
        # Update label untuk menunjukkan bahwa ROI telah di-crop
        if self.last_roi_info and self.last_roi_info['pixmap_size']:
            pixmap_size = self.last_roi_info['pixmap_size']
            lebar = pixmap_size['width']
            tinggi = pixmap_size['height']
            roi_text = f"ROI Cropped: {lebar} x {tinggi} px"
            self.roi_nom.setText(roi_text)
            print(f"Test: ROI cropped - {lebar} x {tinggi} piksel")
    
    def test_reset_roi(self):
        """Test reset ROI functionality"""
        print("Testing reset_roi...")
        if hasattr(self.roi_label, 'reset_view'):
            self.roi_label.reset_view()
        
        # Reset informasi ROI
        self.last_roi_info = None
        self.roi_nom.setText("ROI Dipilih: -")
        print("Test: ROI reset completed")

def main():
    app = QApplication(sys.argv)
    
    test_widget = TestROIWidget()
    test_widget.show()
    
    print("Test ROI Functionality")
    print("======================")
    print("1. Gambar kotak ROI dengan mouse drag")
    print("2. Klik 'Test Update ROI Info' untuk update manual")
    print("3. Klik 'Test Crop ROI' untuk test crop")
    print("4. Klik 'Test Reset ROI' untuk test reset")
    print("5. Perhatikan label 'ROI Dipilih' di atas")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
