# File: autofocus_worker.py
# Versi final yang menggabungkan Coarse-to-Fine Continuous Sweep dengan Early Stopping.

from PyQt5.QtCore import QObject, pyqtSignal, pyqtSlot, QTimer
from PyQt5.QtWidgets import QApplication
import cv2
import numpy as np
import time

class AutoFocusWorker(QObject):
    """
    Worker yang berjalan di thread terpisah untuk melakukan proses auto focus
    tanpa membekukan antarmuka pengguna (UI).
    """
    # Sinyal untuk komunikasi kembali ke UI
    status_changed = pyqtSignal(str)
    focus_finished = pyqtSignal(float, float)
    curve_data_ready = pyqtSignal(dict, dict)

    def __init__(self, camera_worker, grbl_controller, parent=None):
        super().__init__(parent)
        self.camera = camera_worker
        self.grbl = grbl_controller
        self.is_running = False
        self.af_timer = None
        
        # Variabel untuk mengelola proses dua tahap
        self.is_in_fine_scan = False
        
        # Properti untuk sapuan saat ini
        self.current_z_start = 0.0
        self.current_z_end = 0.0
        self.current_speed_mmps = 0.0
        self.total_move_duration = 0.0
        self.move_start_time = 0.0
        
        # Properti untuk melacak fokus
        self.focus_scores = []
        self.z_positions = []
        self.best_focus_score = -1.0
        self.best_focus_z = 0.0

        # Properti untuk 'patience' dan benchmarking
        self.patience_counter = 0
        self.PATIENCE_LIMIT = 5        
        self.MIN_SCORE_THRESHOLD = 5.0 
        self.total_calc_time = 0.0
        self.calc_count = 0
        self.calc_times = []
        self.prev_timestamp = None

        self.phase_start_time = 0.0
        self.phase_end_time = 0.0
        self.coarse_duration = 0.0
        self.fine_duration = 0.0

    def _calculate_focus_score(self, frame):
        """Versi teroptimal untuk menghitung skor fokus."""
        if frame is None or frame.size == 0:
            return 0.0, 0.0

        start_t = time.perf_counter()
        try:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            small_gray = cv2.resize(gray, (0, 0), fx=0.2, fy=0.2)

            gx = cv2.Sobel(small_gray, cv2.CV_16S, 1, 0, ksize=1)
            gy = cv2.Sobel(small_gray, cv2.CV_16S, 0, 1, ksize=1)

            abs_gx = cv2.convertScaleAbs(gx)
            abs_gy = cv2.convertScaleAbs(gy)
            Gmag_sq = cv2.addWeighted(abs_gx, 0.5, abs_gy, 0.5, 0)

            score = Gmag_sq.mean()
            duration = time.perf_counter() - start_t
            return score, duration
        except Exception as e:
            print(f"Error di _calculate_focus_score: {e}")
            return 0.0, 0.0

    @pyqtSlot()
    def run_autofocus(self):
        """Metode utama yang dipanggil UI. Mengorkestrasi proses AF dua tahap."""
        if self.is_running:
            return
            
        self.is_running = True
        self.is_in_fine_scan = False
        self.total_calc_time, self.calc_count = 0.0, 0
        
        self.status_changed.emit("--- Tahap 1: Pencarian Kasar ---")
        print("\n" + "="*50)
        print("[AF START] Memulai proses Auto Focus dua tahap...")
        
        self._start_continuous_sweep(start_z=9.0, end_z=10.5, feed_rate_mmpm=10.0)

    def _start_continuous_sweep(self, start_z, end_z, feed_rate_mmpm):
        """Metode helper untuk memulai satu sapuan kontinu menggunakan estimasi waktu."""
        self.focus_scores, self.z_positions = [], []
        self.best_focus_score, self.best_focus_z = -1.0, 0.0
        self.patience_counter = 0
        self.calc_times = []

        self.current_z_start = start_z
        self.current_z_end = end_z
        self.current_speed_mmps = feed_rate_mmpm / 60.0
        distance = abs(self.current_z_end - self.current_z_start)
        self.total_move_duration = distance / self.current_speed_mmps if self.current_speed_mmps > 0 else 0
        jog_direction = distance if end_z > start_z else -distance
        
        print(f"[AF SWEEP] Parameter: StartZ={start_z:.3f}, EndZ={end_z:.3f}, Jarak={distance:.3f} mm, Kecepatan={feed_rate_mmpm} mm/min")
        print(f"[AF SWEEP] Estimasi Durasi: ~{self.total_move_duration:.2f} detik")

        self.grbl.move_to_z(self.current_z_start)
        self.grbl.wait_for_idle()
        time.sleep(0.3)
        
        self.status_changed.emit(f"Memulai sapuan (Durasi Maks: ~{self.total_move_duration:.2f}s)...")
        self.grbl.send_command(f"$J=G91 Z{jog_direction:.3f} F{feed_rate_mmpm}")
        self.move_start_time = time.time()
        
        if not self.af_timer:
            self.af_timer = QTimer()
            self.af_timer.timeout.connect(self.measure_focus_continuous)
        self.phase_start_time = time.time()
        self.af_timer.start(50)

    def measure_focus_continuous(self):
        """Dipanggil oleh QTimer untuk mengukur fokus selama gerakan kontinu."""
        if not self.is_running:
            if self.af_timer:
                self.af_timer.stop()
            return

        elapsed_time = time.time() - self.move_start_time
        if elapsed_time >= self.total_move_duration:
            print("[AF SWEEP] Sapuan selesai karena durasi maksimal tercapai.")
            self.finish_sweep()
            return

        # Hitung posisi Z saat ini selama sapuan
        current_z = self.current_z_start + (
            elapsed_time * self.current_speed_mmps * np.sign(self.current_z_end - self.current_z_start)
        )

        # Ambil frame terbaru dari kamera
        frame = self.camera.get_latest_numpy_frame()
        timestamp = time.time()
        if self.prev_timestamp is not None:
            delta = (timestamp - self.prev_timestamp) * 1000  # konversi ke ms
            print(f"[CAM] Frame timestamp: {timestamp:.6f} (+{delta:.2f} ms)")
        else:
            print(f"[CAM] Frame timestamp: {timestamp:.6f} (initial frame)")

        self.prev_timestamp = timestamp

        if frame is not None:
            # Hitung skor fokus dan durasi kalkulasi dalam satu langkah
            score, duration = self._calculate_focus_score(frame)

            # Simpan hasil kalkulasi
            self.total_calc_time += duration
            self.calc_count += 1
            self.focus_scores.append(score)
            self.z_positions.append(current_z)

            if 0.0001 < duration < 1.0:
                self.calc_times.append(duration)
            else:
                print(f"[WARNING] Frame {self.calc_count}: durasi aneh ({duration:.4f} detik), diabaikan.")

            # Debug info per frame
            print(f"[DEBUG] Frame {self.calc_count}: Skor={score:.2f}, Z={current_z:.4f}, Durasi={duration*1000:.2f} ms")

            # Logika pencarian fokus terbaik
            if score > self.best_focus_score:
                self.best_focus_score = score
                self.best_focus_z = current_z
                self.patience_counter = 0
            else:
                self.patience_counter += 1

            # Kirim update status ke UI
            self.status_changed.emit(
                f"Skor: {score:.2f} di Z≈{current_z:.3f} (Patience: {self.patience_counter})"
            )

            # Cek apakah perlu berhenti karena 'patience' terpenuhi
            if self.patience_counter >= self.PATIENCE_LIMIT and self.best_focus_score > self.MIN_SCORE_THRESHOLD:
                print("[AF SWEEP] Puncak terdeteksi. Menghentikan sapuan lebih awal.")
                self.finish_sweep()
                return
            
    def finish_sweep(self):
        """Router yang dipanggil setelah setiap sapuan selesai."""
        if self.af_timer: self.af_timer.stop()
        self.grbl.send_command("!"); time.sleep(0.1); self.grbl.send_command("~"); time.sleep(0.1)

        self.phase_end_time = time.time()
        phase_duration = self.phase_end_time - self.phase_start_time

        if not self.is_in_fine_scan:
            self.coarse_duration = phase_duration
        else:
            self.fine_duration = phase_duration

        if not self.is_in_fine_scan:
            self.status_changed.emit(f"Hasil Kasar: Z={self.best_focus_z:.3f}. Memulai Pencarian Halus...")
            print(f"[AF TRANSITION] Hasil Kasar: Z={self.best_focus_z:.3f}. Memulai Pencarian Halus...")
            self.coarse_scan = {
                "z_start": self.current_z_start,
                "z_positions": self.z_positions,
                "focus_scores": self.focus_scores,
                "calc_times": self.calc_times
                }
            self.is_in_fine_scan = True
            
            _, _, last_pos_after_coarse = self.grbl.get_current_position()
            fine_range = 0.1
            fine_end_z = self.best_focus_z - fine_range if last_pos_after_coarse > self.best_focus_z else self.best_focus_z + fine_range
            
            self._start_continuous_sweep(start_z=last_pos_after_coarse, end_z=fine_end_z, feed_rate_mmpm=1.0)
        
        else:
            self.status_changed.emit(f"Fokus final ditemukan di Z={self.best_focus_z:.3f}. Bergerak ke sana...")
            print(f"[AF FINISH] Fokus final ditemukan di Z={self.best_focus_z:.4f}")
            self.fine_scan = {
                "z_positions": self.z_positions,
                "focus_scores": self.focus_scores,
                "calc_times": self.calc_times
                }
            
            self.finish_process(self.best_focus_z, self.best_focus_score)

    def finish_process(self, final_pos, final_score):
        self.is_running = False

        self.grbl.move_to_z(final_pos)
        self.grbl.wait_for_idle()

        print("-" * 50)
        print("[AF STATS] Laporan Performa Akhir:")

        coarse_count = len(self.coarse_scan["focus_scores"])
        fine_count = len(self.fine_scan["focus_scores"])

        print(f"  -> [Coarse] Frame       : {coarse_count} frame")
        print(f"     Durasi               : {self.coarse_duration:.2f} detik")
        fps1 = coarse_count / self.coarse_duration
        print(f"     fps                  : {fps1:.2f} fps")

        print(f"  -> [Fine]   Frame       : {fine_count} frame")
        print(f"     Durasi               : {self.fine_duration:.2f} detik")
        fps = fine_count / self.fine_duration
        print(f"     fps                  : {fps:.2f} fps")

        print(f"  -> Total Frame          : {self.calc_count} frame")
        print(f"     Total Durasi         : {(self.coarse_duration + self.fine_duration):.2f} detik")

        if self.calc_times:
            times_ms = [t * 1000 for t in self.calc_times]
            avg_time = np.mean(times_ms)
            min_time = np.min(times_ms)
            max_time = np.max(times_ms)
            print(f"  -> Waktu per Frame      : Rata-rata {avg_time:.2f} ms, Min {min_time:.2f} ms, Max {max_time:.2f} ms")
        else:
            print("  -> Waktu per Frame      : [tidak valid atau kosong]")

        print(f"  -> Posisi Fokus Akhir   : Z = {final_pos:.4f}")
        print("-" * 50)

        self.grbl.moveToThread(QApplication.instance().thread())
        self.focus_finished.emit(final_pos, final_score)
        self.curve_data_ready.emit(self.coarse_scan, self.fine_scan)

    @pyqtSlot()
    def stop_autofocus(self):
        """Slot untuk menghentikan proses dari luar jika diperlukan."""
        self.is_running = False
        if self.af_timer: self.af_timer.stop()
        self.grbl.send_command("!")
        self.status_changed.emit("Auto Focus dihentikan paksa.")

    @pyqtSlot()
    def run_refinement_autofocus(self):
        """
        Versi final 'Hill Climbing' dengan adaptive step size untuk penyempurnaan fokus.
        """
        self.is_running = True
        self.status_changed.emit("--- Memulai AF Penyempurnaan ---")
        print("\n" + "="*50)
        print("[AF REFINE] Proses penyempurnaan dimulai.")

        step_size = 0.05
        min_step_size = 0.002  # Batas bawah step
        Z_LIMIT_MAX = 11
        adaptive_patience = 3  # Jika skor menurun selama N kali, perkecil step

        self.grbl.wait_for_idle()
        _, _, initial_z = self.grbl.get_current_position()

        pos_bawah = initial_z - step_size
        pos_tengah = initial_z
        pos_atas = initial_z + step_size

        # --- Cek posisi tengah
        self.grbl.move_to_z(pos_tengah)
        self.grbl.wait_for_idle()
        time.sleep(0.1)
        frame_tengah = self.camera.get_latest_numpy_frame()
        skor_tengah, _ = self._calculate_focus_score(frame_tengah)
        print(f"[AF REFINE] Tes Tengah (Z={pos_tengah:.4f}), Skor={skor_tengah:.2f}")

        # --- Cek posisi atas
        self.grbl.move_to_z(pos_atas)
        self.grbl.wait_for_idle()
        time.sleep(0.1)
        frame_atas = self.camera.get_latest_numpy_frame()
        skor_atas, _ = self._calculate_focus_score(frame_atas)
        print(f"[AF REFINE] Tes Atas (Z={pos_atas:.4f}), Skor={skor_atas:.2f}")

        if skor_atas > skor_tengah:
            direction = 1
            best_score = skor_atas
            best_position = pos_atas
            print("[AF REFINE] Arah pencarian: NAIK (+)")
        else:
            direction = -1
            best_score = skor_tengah
            best_position = pos_tengah
            print("[AF REFINE] Arah pencarian: TURUN (-)")

        current_z = best_position

        while self.is_running and step_size >= min_step_size:
            no_improve = 0

            while no_improve < adaptive_patience:
                current_z += direction * step_size

                if not (0 < current_z < Z_LIMIT_MAX):
                    print("[AF REFINE] STOP: Batas aman Z tercapai.")
                    break

                self.grbl.move_to_z(current_z)
                self.grbl.wait_for_idle()
                time.sleep(0.1)

                frame = self.camera.get_latest_numpy_frame()
                score, _ = self._calculate_focus_score(frame)
                print(f"[AF REFINE] Z={current_z:.4f}, Skor={score:.2f} (Terbaik: {best_score:.2f})")

                if score > best_score:
                    best_score = score
                    best_position = current_z
                    no_improve = 0
                else:
                    no_improve += 1
                    print(f"[AF REFINE] -> Skor menurun. NoImprove={no_improve}/{adaptive_patience}")

            # Setelah loop dalam, perkecil step
            step_size *= 0.5
            print(f"[AF REFINE] Adaptif: step size dikurangi jadi {step_size:.4f}")

        self.status_changed.emit(f"Penyempurnaan selesai. Pindah ke posisi final: Z={best_position:.3f}")
        self.grbl.move_to_z(best_position)
        self.grbl.wait_for_idle()
        self.grbl.moveToThread(QApplication.instance().thread())
        self.focus_finished.emit(best_position, best_score)
        self.is_running = False
        print(f"[AF REFINE] Proses selesai. Posisi final: Z={best_position:.4f}\n" + "="*50)
