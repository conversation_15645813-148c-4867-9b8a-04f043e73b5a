# Slide Scanner Application #

This is a Slide Scanner application built with PyQt5 for controlling microscopy equipment. Here's a brief overview:

## Core Components ##

### Main Application (`main.py`) ###
Entry point with splash screen and main window initialization.

### UI Controller (`UI.py`) ###
Main orchestrator that manages the user interface and coordinates between different subsystems through Qt signals/slots.

### Camera System ###
- `Main_Cam.py`: Toupcam SDK wrapper running in separate thread
- `Prev_Cam.py`: USB preview camera handler  
- `Settings.py`: Camera configuration interface

### Motion Control (`Grbl/Grbl.py`) ###
Serial communication with GRBL-controlled motors for XYZ positioning.

### Auto Focus (`AF.py`) ###
- Two-stage coarse-to-fine focus algorithm
- Real-time focus scoring using Sobel edge detection
- Plotting capabilities for focus curve analysis

### Key Features ###
- **Multi-thread architecture** to keep UI respinsive
- **Live camera streaming** with multiple resolution support
- **Motorized stage control** with keyboard shortcuts
- **Automated focusing** with visual feedback
- **Flat-field correction (FFC)** for image enhancement
- **Tabbed interface** for different views (Live Stream, Stitch)
- **Zoom presets** (4X, 10X, 20X, 40X) with optimized camera settings

The application follows a clean separation of concerns with the UI thread handling user interactions while worker threads manage hardware communication

