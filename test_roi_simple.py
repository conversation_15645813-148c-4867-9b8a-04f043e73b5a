#!/usr/bin/env python3
"""
Simple test untuk memverifikasi bahwa implementasi ROI berfungsi
tanpa memerlukan GUI yang kompleks.
"""

import sys
import os

# Test import
try:
    sys.path.append(os.path.join(os.path.dirname(__file__), 'Software'))
    from PyQt5.QtCore import QRect, QPoint
    from PyQt5.QtGui import QPixmap
    print("✓ PyQt5 import berhasil")
except ImportError as e:
    print(f"✗ Error import PyQt5: {e}")
    sys.exit(1)

try:
    from Camera.RoiLabel import ROILabel
    print("✓ ROILabel import berhasil")
except ImportError as e:
    print(f"✗ Error import ROILabel: {e}")
    sys.exit(1)

def test_roi_size_method():
    """Test method get_roi_size() di ROILabel"""
    print("\n=== Test get_roi_size() method ===")
    
    # Buat instance ROILabel
    roi_label = ROILabel()
    
    # Test tanpa ROI
    result = roi_label.get_roi_size()
    if result is None:
        print("✓ get_roi_size() mengembalikan None ketika tidak ada ROI")
    else:
        print("✗ get_roi_size() seharusnya mengembalikan None ketika tidak ada ROI")
    
    # Simulasi ROI rect
    roi_label.roi_rect = QRect(10, 10, 100, 80)
    
    # Setup pixmap dummy
    pixmap = QPixmap(640, 480)
    roi_label.original_pixmap = pixmap
    
    # Test dengan ROI
    result = roi_label.get_roi_size()
    if result and 'pixmap_size' in result and 'screen_size' in result:
        print("✓ get_roi_size() mengembalikan struktur data yang benar")
        print(f"  Screen size: {result['screen_size']}")
        print(f"  Pixmap size: {result['pixmap_size']}")
    else:
        print("✗ get_roi_size() tidak mengembalikan struktur data yang benar")
        print(f"  Result: {result}")

def test_ui_roi_logic():
    """Test logika update_roi_info seperti di UI.py"""
    print("\n=== Test UI ROI Logic ===")
    
    # Simulasi variabel UI
    last_roi_info = None
    roi_nom_text = "ROI Dipilih: -"
    
    # Simulasi ROI label
    roi_label = ROILabel()
    roi_label.roi_rect = QRect(20, 20, 150, 100)
    pixmap = QPixmap(800, 600)
    roi_label.original_pixmap = pixmap
    
    # Test update_roi_info logic
    roi_size_info = roi_label.get_roi_size()
    
    if roi_size_info and roi_size_info['pixmap_size']:
        last_roi_info = roi_size_info
        pixmap_size = roi_size_info['pixmap_size']
        lebar = pixmap_size['width']
        tinggi = pixmap_size['height']
        roi_nom_text = f"ROI Dipilih: {lebar} x {tinggi} px"
        print(f"✓ ROI info berhasil diupdate: {roi_nom_text}")
    else:
        print("✗ Gagal mendapatkan ROI info")
    
    # Test crop scenario
    if last_roi_info and last_roi_info['pixmap_size']:
        pixmap_size = last_roi_info['pixmap_size']
        lebar = pixmap_size['width']
        tinggi = pixmap_size['height']
        roi_nom_text = f"ROI Cropped: {lebar} x {tinggi} px"
        print(f"✓ ROI info setelah crop: {roi_nom_text}")
    
    # Test reset scenario
    last_roi_info = None
    roi_nom_text = "ROI Dipilih: -"
    print(f"✓ ROI info setelah reset: {roi_nom_text}")

def main():
    print("Test ROI Functionality - Simple Version")
    print("=" * 50)
    
    test_roi_size_method()
    test_ui_roi_logic()
    
    print("\n" + "=" * 50)
    print("✓ Semua test selesai!")
    print("\nImplementasi ROI sudah siap:")
    print("1. Label roi_nom akan menampilkan ukuran ROI yang dipilih")
    print("2. Informasi ROI akan tetap ada setelah crop")
    print("3. Reset ROI akan menghapus informasi")

if __name__ == "__main__":
    main()
